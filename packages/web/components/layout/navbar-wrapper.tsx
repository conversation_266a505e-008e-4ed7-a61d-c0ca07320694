import { ClientNavbar } from './client-navbar'
import { getLocale } from '@/lib/locale'
import { Suspense } from 'react'
import { StaticNavbar } from './static-navbar'
import { defaultLocale } from '@/lib/i18n'

// Fallback component for SSG
function NavbarFallback() {
  return <StaticNavbar />;
}

async function NavbarWithLocale() {
  const locale = await getLocale();
  return <ClientNavbar locale={locale} />;
}

export function NavbarWrapper() {
  return (
    <Suspense fallback={<NavbarFallback />}>
      <NavbarWithLocale />
    </Suspense>
  );
}