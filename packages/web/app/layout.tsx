import './globals.css';
import type { <PERSON>ada<PERSON>, Viewport } from 'next';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { JotaiProvider } from '@/components/providers/jotai-provider';
import { QueryProvider } from '@/components/providers/query-provider';
import { NavbarWrapper } from '@/components/layout/navbar-wrapper';
import { Toaster } from '@/components/ui/sonner';
import { getLocale } from '@/lib/locale';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  title: 'OnlyRules - AI Prompt Management Platform',
  description: 'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.',
  keywords: 'AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>cent Cloud CodeBuddy',
  authors: [{ name: 'OnlyRules Team' }],
  publisher: 'OnlyRules',
  category: 'Technology',
  classification: 'Software Development Tools',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
    type: 'website',
    locale: 'en_US',
    siteName: 'OnlyRules',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
  },
  alternates: {
    canonical: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'hsl(0 0% 100%)' },
    { media: '(prefers-color-scheme: dark)', color: 'hsl(240 10% 3.9%)' },
  ],
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get locale asynchronously
  const locale = await getLocale();

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        {/* Google tag (gtag.js) */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-CYBBJ5J4SH"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-CYBBJ5J4SH');
            `,
          }}
        />
        {/* Structured Data for SEO and AdSense */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "OnlyRules",
              "description": "AI Prompt Management Platform for IDEs",
              "url": process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
              "sameAs": [
                "https://toolsdk.ai"
              ],
              "publisher": {
                "@type": "Organization",
                "name": "OnlyRules",
                "description": "AI Prompt Management Platform"
              },
              "potentialAction": {
                "@type": "SearchAction",
                "target": {
                  "@type": "EntryPoint",
                  "urlTemplate": `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/search?q={search_term_string}`
                },
                "query-input": "required name=search_term_string"
              },
              "mainEntity": {
                "@type": "SoftwareApplication",
                "name": "OnlyRules",
                "applicationCategory": "DeveloperApplication",
                "offers": {
                  "@type": "Offer",
                  "price": "0",
                  "priceCurrency": "USD"
                },
                "operatingSystem": "Web Browser",
                "description": "Create, organize, and share AI prompt rules for your favorite IDEs"
              }
            })
          }}
        />
        {/* Preconnect to external domains for better performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://www.googletagservices.com" />
        <link rel="preconnect" href="https://www.google.com" />
        <link rel="preconnect" href="https://googleads.g.doubleclick.net" />
      </head>
      <body className="min-h-screen bg-background font-sans antialiased">
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            <JotaiProvider>
            <div className="relative flex min-h-screen flex-col">
              <NavbarWrapper />
              <main className="flex-1">
                {children}
              </main>
              <footer className="border-t border-border">
                <div className="mobile-container py-6 xs:py-8">
                  <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                                    <div className="flex flex-col md:flex-row items-center gap-4">
                  <p className="text-xs xs:text-sm text-center text-muted-foreground">
                    Built with ❤️ for the AI coding community.
                  </p>
                </div>
                <div className="flex flex-col md:flex-row items-center gap-4">
                  <div className="flex flex-wrap items-center gap-4">
                    <a
                      href="/terms"
                      className="text-xs xs:text-sm text-primary hover:underline focus-visible-ring rounded touch-target"
                    >
                      Terms of Service
                    </a>
                    <a
                      href="/privacy"
                      className="text-xs xs:text-sm text-primary hover:underline focus-visible-ring rounded touch-target"
                    >
                      Privacy Policy
                    </a>
                    <a
                      href="https://toolsdk.ai/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs xs:text-sm text-primary hover:underline focus-visible-ring rounded touch-target"
                    >
                      ToolSDK.ai
                    </a>
                  </div>
                </div>
                  </div>
                </div>
              </footer>
            </div>
            <Toaster 
              position="top-center"
              toastOptions={{
                style: {
                  fontSize: '14px',
                },
                className: 'text-sm',
              }}
            />
            </JotaiProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}