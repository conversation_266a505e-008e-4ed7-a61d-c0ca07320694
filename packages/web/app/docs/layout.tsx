import { DocsLayout } from 'fumadocs-ui/layouts/docs';
import type { ReactNode } from 'react';
import { source } from '@/lib/source';
import { RootProvider } from 'fumadocs-ui/provider';
import { Theme } from '@radix-ui/themes';
import './docs.css';

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <Theme
      accentColor="blue"
      grayColor="slate"
      radius="medium"
      scaling="100%"
      appearance="dark"
      panelBackground="translucent"
      hasBackground={false}
    >
      <RootProvider>
        <DocsLayout
          tree={{
            name: 'Docs',
            children: source.pageTree as any, // Fix type error by casting to 'any'
          }}
          nav={{
            title: 'OnlyRules Docs',
            url: '/docs',
          }}
          sidebar={{
            defaultOpenLevel: 1,
          }}
          links={[
            {
              text: 'Home',
              url: '/',
            },
            {
              text: 'Dashboard',
              url: '/dashboard',
            },
            {
              text: 'GitHub',
              url: 'https://github.com/ranglang/onlyrules',
              external: true,
            },
          ]}
        >
          {children}
        </DocsLayout>
      </RootProvider>
    </Theme>
  );
}
