import { Metadata } from "next";

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

interface PageProps {
  params: {
    id: string;
  };
  searchParams: {
    token?: string;
  };
}

// Static metadata for the page
export const metadata: Metadata = {
  title: "OnlyRules - AI Rule Sets",
  description: "AI rule sets for developers - collections of rules for quick setup"
};

export default async function RulesetPage({ params, searchParams }: PageProps) {
  // Temporary placeholder during build - this will be replaced with proper implementation
  return (
    <div className="container max-w-4xl py-8">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Loading Ruleset...</h1>
        <p className="text-muted-foreground">Ruleset ID: {params.id}</p>
        {searchParams.token && (
          <p className="text-sm text-muted-foreground">Access Token: {searchParams.token}</p>
        )}
        <p className="text-sm text-muted-foreground mt-4">
          This page is being built. Please check back soon.
        </p>
      </div>
    </div>
  );
}