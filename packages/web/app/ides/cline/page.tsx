import React from 'react';
import { Metada<PERSON> } from 'next';
import IDEPageTemplate from '@/components/ide-page-template';
import { Bot, FileCode, GitMerge, Terminal } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Cline Integration - Autonomous AI Coding Agent',
  description: 'Learn how to use Cline autonomous AI agent with OnlyRules. Master multi-file editing, complex refactoring, and autonomous development workflows.',
  keywords: 'Cline AI, autonomous coding, OnlyRules Cline, VS Code Cline, AI agent, multi-file editing, autonomous development, AI coding assistant',
  alternates: {
    canonical: '/ides/cline',
  },
  openGraph: {
    title: 'Cline Autonomous AI Agent - OnlyRules Integration',
    description: 'Unlock autonomous AI development with Cline and OnlyRules custom prompts.',
    images: ['/images/cline-integration-og.png'],
  },
};

const clineData = {
  ide: {
    name: 'Cline',
    icon: '🎯',
    color: 'bg-red-500',
    description: 'Autonomous AI assistant for complex coding tasks',
    website: 'https://marketplace.visualstudio.com/items?itemName=saoudrizwan.claude-dev',
    version: '2.0+',
  },
  installation: {
    steps: [
      {
        title: 'Install VS Code',
        description: 'Cline runs as a VS Code extension. Make sure you have VS Code installed.',
        command: 'https://code.visualstudio.com/download',
      },
      {
        title: 'Install Cline Extension',
        description: 'Search for "Cline" in VS Code Extensions marketplace and install it.',
        command: 'ext install saoudrizwan.claude-dev',
        note: 'Cline is the new name for what was previously "Claude Dev".',
      },
      {
        title: 'Configure AI Provider',
        description: 'Set up your preferred AI provider (Claude, OpenAI, or local models).',
        command: 'Cmd/Ctrl + , → Extensions → Cline → API Configuration',
        note: 'Cline supports multiple AI providers. Claude 3.5 Sonnet recommended for best results.',
      },
      {
        title: 'Grant Permissions',
        description: 'Allow Cline to access your file system and terminal for autonomous operations.',
        note: 'Cline will ask for permission before making changes. You can configure auto-approval for trusted operations.',
      },
    ],
  },
  integration: {
    steps: [
      {
        title: 'Access Cline Templates',
        description: 'Find Cline-specific autonomous coding templates on OnlyRules.',
        code: 'https://onlyrules.app/templates?ide=CLINE',
      },
      {
        title: 'Create Task Definitions',
        description: 'Set up a .cline directory with task templates for common operations.',
        code: `mkdir .cline
touch .cline/tasks.md
touch .cline/permissions.json`,
      },
      {
        title: 'Define Autonomous Tasks',
        description: 'Create detailed task definitions that Cline can execute autonomously.',
        code: `# .cline/tasks.md

## Feature Implementation Task
When implementing a new feature:
1. Analyze requirements and create implementation plan
2. Create necessary files and directories
3. Implement core functionality with error handling
4. Add comprehensive tests
5. Update documentation
6. Run tests and fix any issues
7. Create pull request summary

## Refactoring Task
For refactoring operations:
1. Analyze current implementation
2. Identify improvement opportunities
3. Create refactoring plan
4. Implement changes incrementally
5. Ensure tests pass after each change
6. Update affected documentation

## Bug Fix Task
When fixing bugs:
1. Reproduce the issue
2. Identify root cause
3. Implement fix with minimal changes
4. Add regression tests
5. Verify fix doesn't break existing functionality`,
      },
      {
        title: 'Configure Permissions',
        description: 'Set up permission rules for autonomous operations.',
        code: `{
  "autoApprove": {
    "fileCreation": ["src/**/*.test.ts", "docs/**/*.md"],
    "fileModification": ["*.json", "*.md"],
    "terminalCommands": ["npm test", "npm run lint"]
  },
  "requireApproval": {
    "fileCreation": ["**/*.ts", "**/*.tsx"],
    "fileDeletion": ["**/*"],
    "terminalCommands": ["npm install", "git push"]
  },
  "forbidden": {
    "paths": [".env", "secrets/**", "*.key"],
    "commands": ["rm -rf", "sudo"]
  }
}`,
      },
    ],
  },
  features: [
    {
      title: 'Autonomous Execution',
      description: 'Completes complex multi-step tasks without constant supervision',
      icon: 'bot',
    },
    {
      title: 'Multi-File Operations',
      description: 'Edits multiple files coherently to implement features',
      icon: 'file-code',
    },
    {
      title: 'Terminal Integration',
      description: 'Runs commands, tests, and tools as part of development workflow',
      icon: 'terminal',
    },
  ],
  examples: [
    {
      title: 'Full Feature Implementation',
      description: 'Build a complete feature from specification to tests',
      prompt: `Implement a user notification system with the following requirements:
- Email and in-app notifications
- User preferences for notification types
- Queue system for reliable delivery
- Admin dashboard for monitoring
- Complete test coverage
- API documentation

Use our standard architecture patterns and include all necessary files.`,
      result: 'Complete notification system with all components, tests, and documentation',
    },
    {
      title: 'Codebase Migration',
      description: 'Migrate entire codebase to new framework or pattern',
      prompt: `Migrate our Express.js API to Fastify:
1. Analyze all current routes and middleware
2. Set up Fastify with equivalent configuration
3. Migrate routes preserving all functionality
4. Update tests for Fastify
5. Ensure backward compatibility
6. Update deployment configuration
7. Create migration guide

Preserve all existing functionality and API contracts.`,
      result: 'Complete migration with all routes, tests, and documentation updated',
    },
    {
      title: 'Automated Debugging',
      description: 'Autonomously debug and fix complex issues',
      prompt: `Debug and fix the performance issue in our data processing pipeline:
- Profile the current implementation
- Identify bottlenecks
- Implement optimizations
- Add performance benchmarks
- Ensure correctness with tests
- Document the changes and improvements

The pipeline should handle 10x current load after optimization.`,
      result: 'Optimized pipeline with benchmarks showing 10x performance improvement',
    },
  ],
  tips: [
    'Start with small, well-defined tasks to build confidence in Cline\'s autonomous capabilities',
    'Use detailed task descriptions from OnlyRules templates for best results',
    'Configure permissions carefully to maintain security while enabling productivity',
    'Review Cline\'s proposed changes before applying them to critical code',
    'Use Cline\'s planning mode to review approach before execution',
    'Combine multiple small tasks into larger workflows for complex features',
    'Let Cline run tests and fix issues iteratively for better results',
    'Use version control to easily revert if needed',
    'Create project-specific task templates for common operations',
    'Monitor Cline\'s actions through the activity panel for learning and debugging',
  ],
};

export default function ClinePage() {
  // Convert string identifiers to JSX icons
  const iconMap = {
    bot: <Bot className="h-5 w-5" />,
    'file-code': <FileCode className="h-5 w-5" />,
    terminal: <Terminal className="h-5 w-5" />,
  };

  const featuresWithIcons = clineData.features.map(feature => ({
    ...feature,
    icon: iconMap[feature.icon as keyof typeof iconMap],
  }));

  return <IDEPageTemplate {...clineData} features={featuresWithIcons} />;
}