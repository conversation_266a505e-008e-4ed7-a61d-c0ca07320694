"use client";

import { useEffect, useState, useMemo } from "react";

export const dynamic = 'force-dynamic';
import {
  <PERSON>ton,
  TextField,
  Select,
  Badge,
  Card,
  Tabs,
  Dialog,
  Text,
  Heading,
  Box,
  Flex
} from "@radix-ui/themes";
import { Plus, Search, Filter, Code, Users, Eye, Lock } from "lucide-react";
import { RuleCard } from "@/components/rule-card";
import { RuleEditor } from "@/components/rule-editor";
import { useSession } from "@/lib/auth-client";
import { toast } from "sonner";
import { Rule, Tag, RulePayload } from "@/lib/store";
import { useRules, useInvalidateRules } from "@/hooks/use-rule-queries";
import { useTags } from "@/hooks/use-tag-queries";

export default function DashboardPage() {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedIDE, setSelectedIDE] = useState("ALL");
  const [showEditor, setShowEditor] = useState(false);
  const [editingRule, setEditingRule] = useState<Rule | null>(null);
  
  // Memoize the filters object to prevent unnecessary re-renders
  const ruleFilters = useMemo(() => ({
    search: searchQuery || undefined,
    tags: selectedTags.length > 0 ? selectedTags : undefined,
    ideType: selectedIDE !== "ALL" ? selectedIDE : undefined,
  }), [searchQuery, selectedTags, selectedIDE]);
  
  // Use query hooks for data fetching
  const { 
    data: rulesResponse, 
    isLoading: rulesLoading, 
    error: rulesError 
  } = useRules(ruleFilters);
  
  const { 
    data: tags = [], 
    isLoading: tagsLoading 
  } = useTags();
  
  const invalidateRules = useInvalidateRules();
  
  // Extract rules from response
  const rules = rulesResponse?.rules || [];
  const loading = rulesLoading;

  // Debug: Log dialog state changes
  useEffect(() => {
    console.log("Dialog state changed:", showEditor);
  }, [showEditor]);

  // Failsafe: Ensure dialog doesn't get stuck open
  useEffect(() => {
    // Close dialog on escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showEditor) {
        setShowEditor(false);
      }
    };
    
    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [showEditor]);

  // Debug: Check for blocking elements
  useEffect(() => {
    const checkForBlockingElements = () => {
      const elements = document.querySelectorAll('*');
      const blockingElements: Element[] = [];
      
      elements.forEach(el => {
        const style = window.getComputedStyle(el);
        const rect = el.getBoundingClientRect();
        
        // Check for elements that cover the viewport
        if (
          style.position === 'fixed' &&
          rect.width >= window.innerWidth * 0.9 &&
          rect.height >= window.innerHeight * 0.9 &&
          style.pointerEvents !== 'none'
        ) {
          blockingElements.push(el);
        }
      });
      
      if (blockingElements.length > 0) {
        console.warn('Found blocking elements:', blockingElements);
        blockingElements.forEach(el => {
          console.log('Blocking element:', {
            element: el,
            classes: el.className,
            id: el.id,
            zIndex: window.getComputedStyle(el).zIndex,
            display: window.getComputedStyle(el).display,
          });
        });
      }
    };
    
    // Check immediately and after a short delay
    checkForBlockingElements();
    const timer = setTimeout(checkForBlockingElements, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  // Handle query errors
  useEffect(() => {
    if (rulesError) {
      console.error("Error fetching rules:", rulesError);
      toast.error("Failed to fetch rules");
    }
  }, [rulesError]);

  const handleCreateRule = () => {
    setEditingRule(null);
    setShowEditor(true);
  };

  const handleEditRule = (rule: Rule) => {
    setEditingRule(rule);
    setShowEditor(true);
  };

  const handleSaveRule = async (ruleData: Partial<RulePayload>) => {
    try {
      const method = editingRule ? "PUT" : "POST";
      const url = editingRule ? `/api/rules/${editingRule.id}` : "/api/rules";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(ruleData),
      });

      if (response.ok) {
        toast.success(editingRule ? "Rule updated" : "Rule created");
        setShowEditor(false);
        setEditingRule(null); // Also clear editing rule
        invalidateRules(); // Invalidate and refetch rules
      } else {
        toast.error("Failed to save rule");
      }
    } catch (error) {
      console.error("Error saving rule:", error);
      toast.error("Failed to save rule");
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    try {
      const response = await fetch(`/api/rules/${ruleId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Rule deleted");
        invalidateRules(); // Invalidate and refetch rules
      } else {
        toast.error("Failed to delete rule");
      }
    } catch (error) {
      console.error("Error deleting rule:", error);
      toast.error("Failed to delete rule");
    }
  };

  const toggleTag = (tagName: string) => {
    setSelectedTags(prev =>
      prev.includes(tagName)
        ? prev.filter(t => t !== tagName)
        : [...prev, tagName]
    );
  };

  // Temporarily bypass authentication for testing text readability
  const isTestMode = process.env.NODE_ENV === 'development';

  if (!session?.user && !isTestMode) {
    return (
      <div className="container py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4" style={{ color: 'hsl(0 0% 98%)' }}>Please sign in to continue</h1>
          <Button asChild>
            <a href="/auth/signin">Sign In</a>
          </Button>
        </div>
      </div>
    );
  }

  // Use mock session in test mode
  const effectiveSession = session || (isTestMode ? {
    user: {
      id: "test-user-id",
      name: "Test User",
      email: "<EMAIL>",
      image: null
    }
  } : null);

  const userRules = Array.isArray(rules) ? rules.filter(rule => rule.userId === effectiveSession?.user?.id) : [];
  const publicRules = Array.isArray(rules) ? rules.filter(rule => rule.visibility === "PUBLIC") : [];

  const stats = {
    totalRules: userRules.length,
    publicRules: userRules.filter(r => r.visibility === "PUBLIC").length,
    privateRules: userRules.filter(r => r.visibility === "PRIVATE").length,
    totalViews: 0, // Would be calculated from analytics
  };

  // Show loading state for initial load
  if (loading && !rules.length) {
    return (
      <Box className="mobile-container py-6 xs:py-8 space-y-6 xs:space-y-8">
        <div className="text-center">
          <Heading size="6" xs-size="8" weight="bold" color="gray" highContrast className="text-2xl xs:text-3xl">Dashboard</Heading>
          <Text size="2" xs-size="3" color="gray" className="text-sm xs:text-base mb-8">
            Loading your rules...
          </Text>
          <div className="animate-pulse space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 xs:gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-20"></div>
              ))}
            </div>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
              ))}
            </div>
          </div>
        </div>
      </Box>
    );
  }

  return (
    <Box className="mobile-container py-6 xs:py-8 space-y-6 xs:space-y-8">
      {/* Header */}
      <Flex justify="between" align="center" className="flex-col xs:flex-row gap-4 xs:gap-0">
        <Box className="text-center xs:text-left">
          <Heading size="6" xs-size="8" weight="bold" color="gray" highContrast className="text-2xl xs:text-3xl">Dashboard</Heading>
          <Text size="2" xs-size="3" color="gray" className="text-sm xs:text-base">
            Manage your AI prompt rules and explore the community
          </Text>
        </Box>
        <Button onClick={handleCreateRule} className="touch-target w-full xs:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          New Rule
        </Button>
      </Flex>

      {/* Stats */}
      <Box className="grid grid-cols-2 md:grid-cols-4 gap-3 xs:gap-4">
        <Card className="mobile-card">
          <Flex justify="between" align="center" pb="2">
            <Text size="1" xs-size="2" weight="medium" color="gray" highContrast className="text-xs xs:text-sm">Total Rules</Text>
            <Code className="h-3 w-3 xs:h-4 xs:w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="5" xs-size="7" weight="bold" color="gray" highContrast className="text-xl xs:text-2xl">{stats.totalRules}</Text>
        </Card>

        <Card className="mobile-card">
          <Flex justify="between" align="center" pb="2">
            <Text size="1" xs-size="2" weight="medium" color="gray" highContrast className="text-xs xs:text-sm">Public Rules</Text>
            <Users className="h-3 w-3 xs:h-4 xs:w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="5" xs-size="7" weight="bold" color="gray" highContrast className="text-xl xs:text-2xl">{stats.publicRules}</Text>
        </Card>

        <Card className="mobile-card">
          <Flex justify="between" align="center" pb="2">
            <Text size="1" xs-size="2" weight="medium" color="gray" highContrast className="text-xs xs:text-sm">Private Rules</Text>
            <Lock className="h-3 w-3 xs:h-4 xs:w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="5" xs-size="7" weight="bold" color="gray" highContrast className="text-xl xs:text-2xl">{stats.privateRules}</Text>
        </Card>

        <Card className="mobile-card">
          <Flex justify="between" align="center" pb="2">
            <Text size="1" xs-size="2" weight="medium" color="gray" highContrast className="text-xs xs:text-sm">Total Views</Text>
            <Eye className="h-3 w-3 xs:h-4 xs:w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="5" xs-size="7" weight="bold" color="gray" highContrast className="text-xl xs:text-2xl">{stats.totalViews}</Text>
        </Card>
      </Box>

      {/* Filters */}
      <div className="mobile-flex gap-3 xs:gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <TextField.Root
            placeholder="Search rules..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-11 xs:h-12 text-sm xs:text-base"
          />
        </div>
        
        <Select.Root value={selectedIDE} onValueChange={setSelectedIDE}>
          <Select.Trigger className="w-full sm:w-40 md:w-48 h-11 xs:h-12 text-sm xs:text-base" placeholder="IDE Type" />
          <Select.Content>
            <Select.Item value="ALL">All IDEs</Select.Item>
            <Select.Item value="GENERAL">General</Select.Item>
            <Select.Item value="CURSOR">Cursor</Select.Item>
            <Select.Item value="AUGMENT">Augment Code</Select.Item>
            <Select.Item value="WINDSURF">Windsurf</Select.Item>
            <Select.Item value="CLAUDE">Claude</Select.Item>
            <Select.Item value="GITHUB_COPILOT">GitHub Copilot</Select.Item>
            <Select.Item value="GEMINI">Gemini</Select.Item>
            <Select.Item value="OPENAI_CODEX">OpenAI Codex</Select.Item>
            <Select.Item value="CLINE">Cline</Select.Item>
            <Select.Item value="JUNIE">Junie</Select.Item>
            <Select.Item value="TRAE">Trae</Select.Item>
            <Select.Item value="LINGMA">Lingma</Select.Item>
            <Select.Item value="KIRO">Kiro</Select.Item>
            <Select.Item value="TENCENT_CODEBUDDY">Tencent Cloud CodeBuddy</Select.Item>
          </Select.Content>
        </Select.Root>
      </div>

      {/* Tag Filters */}
      {tags.length > 0 && (
        <Box className="space-y-3 xs:space-y-4">
          <Flex align="center" gap="2">
            <Filter className="h-4 w-4" style={{ color: 'var(--gray-12)' }} />
            <Text size="2" weight="medium" color="gray" highContrast className="text-sm xs:text-base">Filter by tags:</Text>
          </Flex>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge
                key={tag.id}
                variant={selectedTags.includes(tag.name) ? "solid" : "outline"}
                className="cursor-pointer touch-target text-xs xs:text-sm px-2 xs:px-3 py-1 xs:py-1.5"
                onClick={() => toggleTag(tag.name)}
                style={{
                  borderColor: tag.color,
                  backgroundColor: selectedTags.includes(tag.name) ? tag.color : "transparent",
                }}
              >
                {tag.name}
              </Badge>
            ))}
          </div>
        </Box>
      )}

      {/* Content */}
      <Tabs.Root defaultValue="my-rules" className="space-y-4 xs:space-y-6">
        <Tabs.List className="w-full">
          <Tabs.Trigger value="my-rules" className="flex-1 text-sm xs:text-base">My Rules ({userRules.length})</Tabs.Trigger>
          <Tabs.Trigger value="community" className="flex-1 text-sm xs:text-base">Community ({publicRules.length})</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="my-rules" className="space-y-4 xs:space-y-6">
          {userRules.length === 0 ? (
            <Card className="py-8 xs:py-12 text-center mobile-card">
              <Code className="h-8 w-8 xs:h-12 xs:w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-base xs:text-lg font-semibold mb-2">No rules yet</h3>
              <p className="text-muted-foreground mb-4 text-sm xs:text-base px-4">
                Create your first AI prompt rule to get started
              </p>
              <Button onClick={handleCreateRule} className="touch-target">
                <Plus className="mr-2 h-4 w-4" />
                Create Rule
              </Button>
            </Card>
          ) : (
            <div className="mobile-grid">
              {userRules.map((rule) => (
                <RuleCard
                  key={rule.id}
                  rule={rule}
                  onEdit={handleEditRule}
                  onDelete={handleDeleteRule}
                  isOwner={true}
                />
              ))}
            </div>
          )}
        </Tabs.Content>

        <Tabs.Content value="community" className="space-y-4 xs:space-y-6">
          {publicRules.length === 0 ? (
            <Card className="py-8 xs:py-12 text-center mobile-card">
              <Users className="h-8 w-8 xs:h-12 xs:w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-base xs:text-lg font-semibold mb-2">No community rules found</h3>
              <p className="text-muted-foreground text-sm xs:text-base px-4">
                Try adjusting your filters or check back later
              </p>
            </Card>
          ) : (
            <div className="mobile-grid">
              {publicRules.map((rule) => (
                <RuleCard
                  key={rule.id}
                  rule={rule}
                  isOwner={rule.userId === effectiveSession?.user?.id}
                />
              ))}
            </div>
          )}
        </Tabs.Content>
      </Tabs.Root>

      {/* Rule Editor Dialog */}
      <Dialog.Root open={showEditor} onOpenChange={setShowEditor}>
        <Dialog.Content className="max-w-4xl max-h-[90vh] w-[95vw] xs:w-[90vw] overflow-hidden">
          <Dialog.Title className="text-lg xs:text-xl">
            {editingRule ? "Edit Rule" : "Create New Rule"}
          </Dialog.Title>
          <div className="overflow-y-auto max-h-[70vh]">
            <RuleEditor
              rule={editingRule || undefined}
              onSave={handleSaveRule}
              onCancel={() => {
                setShowEditor(false);
                setEditingRule(null);
              }}
            />
          </div>
        </Dialog.Content>
      </Dialog.Root>
    </Box>
  );
}