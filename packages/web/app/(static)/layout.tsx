import '../globals.css';
import type { Metadata } from 'next';
// import { ThemeProvider } from '@/components/providers/theme-provider';

// Force dynamic generation
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'OnlyRules - AI Prompt Management Platform',
  description: 'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.',
  keywords: 'AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy',
  authors: [{ name: 'OnlyRules Team' }],
  openGraph: {
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
    type: 'website',
  },
};

export default function StaticLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="dark">
      <div className="min-h-screen bg-background flex flex-col">
        {children}
      </div>
    </div>
  );
}
